package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirSendAppeir;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EirSendAppeirRepository extends JpaRepository<EirSendAppeir, Integer> {

    @Modifying
    @Query("UPDATE EirSendAppeir e SET " +
           "e.flagSend = :flagSend, " +
           "e.statusCode = :statusCode, " +
           "e.resultMessage = :resultMessage, " +
           "e.eirReferenceAppeir = :inspectionId, " +
           "e.modificationDate = CURRENT_TIMESTAMP, " +
           "e.appeirTrace = LEFT(COALESCE(e.appeirTrace, '') + '->sent', 50) " +
           "WHERE e.id = :eirSendAppeirId")
    void updateSendAppEirStatus(@Param("eirSendAppeirId") Integer eirSendAppeirId,
                               @Param("flagSend") Character flagSend,
                               @Param("statusCode") Integer statusCode,
                               @Param("resultMessage") String resultMessage,
                               @Param("inspectionId") Integer inspectionId);
}
