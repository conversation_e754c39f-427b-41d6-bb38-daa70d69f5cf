1.- Tabla [sde].[depot_credential_appeir]

alter table [sde].[depot_credential_appeir] add shipping_line_id int not null,
							business_unit_id numeric(18, 0) not null,
							send_gate_in bit not null,
							send_gate_out bit not null

EXEC sp_rename '[sde].[depot_credential_appeir].shop_email', 'shop_email_copy', 'COLUMN'

2.- Important, delete the records from the table sde.eir_send_appeir

	DELETE sde.eir_send_appeir

3.- Tabla [sde].[eir_send_appeir]

ALTER TABLE [sde].[eir_send_appeir] ADD [depot_credential_appeir_id] [int] NOT NULL

ALTER TABLE [sde].[eir_send_appeir] ADD
	[depot_credential_appeir_id] [int] NOT NULL,
	[sub_business_unit_id] INT NOT NULL,
	[active] [bit] NOT NULL,
	[comment] [varchar](200) NULL,
	appeir_trace varchar(50) null,
	[scope_inspection] [varchar](1) NULL,
	[is_damaged_container] [bit] NULL,
	[estimated_estructure_id] [int] NULL,
	[estimated_machinery_id] [int] NULL,
	[is_new_insert] [bit] NULL,
	[eir_reference_appeir] [int] NULL,
	[activity_date] [datetime] NULL

ALTER TABLE [sde].[eir_send_appeir] ALTER COLUMN result_message [varchar](max)

CREATE INDEX send_appeir_settingX ON [sde].[eir_send_appeir] (depot_credential_appeir_id)
CREATE INDEX send_appeir_sub_business_idX ON [sde].[eir_send_appeir] (sub_business_unit_id)
CREATE INDEX send_appeir_eir_idX ON [sde].[eir_send_appeir] (eir_id)

4.- Test Configuration Record - Example for test

--Register Depot: 86-Illinois | USA :::::: 4104-Maersk Line
INSERT [sde].[depot_credential_appeir]
(sub_business_unit_id,url,client_id,client_secret,shop_email_copy,active,user_registration_id,registration_date,shipping_line_id,business_unit_id,send_gate_in,send_gate_out)
VALUES(86,'https://iam.maersk.com','bLClE1SJnrV','gEO8dB8','<EMAIL>',1,1,GETDATE(),4104,58,1,0)


5.- Store [sde].[get_depot_credentials_appeir]

ALTER PROCEDURE [sde].[get_depot_credentials_appeir]
@system_alias varchar(10) = NULL
AS

/*-----------------------------------------------------------------------------------------
 Ticket     		Author 	Date	   	Description of change
 ---------------	------	----------	-------------------------------------------
 000000000000000	<USER>  <GROUP>/06/2025	Implementation of appEIR transmissions
------------------------------------------------------------------------------------------*/

BEGIN

SET NOCOUNT ON

--ALL CONFIGURED DEPOTS

	SELECT	CRE.depot_credential_appeir_id,
			CRE.sub_business_unit_id,
			CRE.[url],
			CRE.client_id,
			CRE.client_secret,
			CRE.shop_email_copy,
			CRE.[active],
			CRE.user_registration_id,
			CRE.registration_date,
			CRE.user_modification_id,
			CRE.modification_date,
			CRE.shipping_line_id,
			CRE.business_unit_id,
			CRE.send_gate_in,
			CRE.send_gate_out
	FROM	sde.depot_credential_appeir AS CRE (NOLOCK)
	WHERE	CRE.active = 1
	ORDER BY CRE.registration_date
END



6.- Store [sde].[get_pending_transmission_appeir]

ALTER PROCEDURE [sde].[get_pending_transmission_appeir]
@depot_credential_appeir_id INT
AS

/*-----------------------------------------------------------------------------------------
 Ticket     		Author 	Date	   	Description of change
 ---------------	------	----------	-------------------------------------------
 000000000000000	<USER>  <GROUP>/06/2025	Implementation of appEIR transmissions
------------------------------------------------------------------------------------------

"GATE IN" TRANSMISSIONS

Final considerations for the appEIR service for the depots

1.-- All transmissions will be made once the "gate-in" has finished (with the truck departure date), whether inspected or not.
2.-- It is mandatory to send at least one photo for each damage; for damages without photo, a dummy photo will be associated with them.
3.-- A maximum of 3 photos may be sent for each damage.
4.-- Inspections with a result "damaged" will be sent.
5.-- For "gate-in" not yet inspected, they will be sent as the NoDamage flag and with a dummy photo.
6.-- Send sequence:
	 There will be the possibility of sending more than one time (multiple inserts)
6.1.-- In dry containers, there may be up to 2 transmissions:
	- 1st when the "gate in" has finished,
	- 2nd when the inspection has finished and the result is damaged.
6.2.-- In reefer containers, there may be up to 3 transmissions:
	- 1st when the "gate in" has finished,
	- 2nd when structure inspection has finished and the result is damaged.
	- 3rd when machinery inspection has finished and the result is damaged.

*/

BEGIN

SET NOCOUNT ON

DECLARE @tb_gi_pending TABLE(_id int identity, eir_send_appeir_id	int,
						sub_business_unit_id numeric(18),
						eir_id	int,
						scope_inspection	varchar(1),
						is_damaged_container	bit,
						estimated_estructure_id	int,
						estimated_machinery_id	int,
						driver_id varchar(10),
						inspector_id varchar(10),
						container_id int,
						---------------------------
						inspection_date datetime,
						truck_arrival datetime,
						truck_departure datetime,
						activity_date datetime,
						cat_procedencia_id numeric(18),
						is_new_insert bit)

DECLARE @tb_gi_damaged TABLE(eir_send_appeir_id	int,
						sub_business_unit_id numeric(18),
						eir_id	int,
						estimated_id	int,
						scope_inspection	varchar(1),
						is_damaged_container	bit,
						driver_id varchar(10),
						---------------------------
						inspection_date datetime,
						truck_arrival datetime,
						truck_departure datetime,
						activity_date datetime,
						cat_procedencia_id numeric(18),
						is_new_insert bit)

DECLARE @tb_gi_appeir_header TABLE(	_id INT IDENTITY,
									company NUMERIC(18),
									inspectionID INT,
									createdLocalDate VARCHAR(20),
									submittedLocalDate VARCHAR(20),
									comments VARCHAR(1000),
									userid VARCHAR(100),
									containerStatus INT,
									operator INT,
									moveType INT,
									moveStatus INT,
									bookingNo VARCHAR(100),
									containerNo VARCHAR(20),
									email VARCHAR(100),
									categoryEmail VARCHAR(20),
									truckerRegno VARCHAR(20),
									truckerName VARCHAR(300),
									containerType VARCHAR(20),
									containerClass VARCHAR(20),
									containerSize varchar(10),
									equipmentISOCode varchar(10),
									equipmentSubType varchar(10),
									estimate_number int,
									eir_id int,
									scope_inspection varchar(1),
									is_damaged_container bit,
									---------------------------
									inspection_date datetime,
									truck_arrival datetime,
									truck_departure datetime,
									activity_date datetime,
									sub_move_type_id numeric(18),
									is_new_insert bit)

DECLARE @tb_gi_appeir_detail TABLE(	_id INT IDENTITY,
										damageLocation VARCHAR(20),
										damageType VARCHAR(20),
										[description] VARCHAR(200),
										tpIndicator INT,
										panelIndicatorCode VARCHAR(100),
										isDamage BIT,
										panelImageFilename VARCHAR(20),
										missingSpare INT,
										damageCarrierCode INT,
										inspectionDetailID INT,
										inspectionID INT,
										is_dummy bit )

DECLARE @tb_gi_appeir_pic TABLE(	_id INT IDENTITY,
								  inspectionDetailID INT,
								  [name] VARCHAR(200),
								  [extension] VARCHAR(20),
								  [url] VARCHAR(1000), N int)

DECLARE @is_gate_out NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43081'),
        @is_gate_in NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43080')

DECLARE @sub_business_unit_id numeric(18),@send_gate_in bit=0,@send_gate_out bit=0,@shipping_line_id INT,@eir_id INT,@shop_email_copy varchar(200),@credencial_client_id varchar(100),
		@name_bu varchar(200)

SELECT	@shipping_line_id = CRE.shipping_line_id,
		@sub_business_unit_id = CRE.sub_business_unit_id,
		@send_gate_in = CRE.send_gate_in,
		@send_gate_out = CRE.send_gate_out,
		@shop_email_copy = ISNULL(CRE.shop_email_copy,''),
		@credencial_client_id = CRE.client_id,
		@name_bu = bux.nombre
FROM	sde.depot_credential_appeir AS CRE (NOLOCK)
INNER JOIN seg.unidad_negocio as bux (nolock) ON CRE.sub_business_unit_id = bux.unidad_negocio_id
WHERE	CRE.depot_credential_appeir_id = @depot_credential_appeir_id

IF @send_gate_in = 1
BEGIN
	--All pending with "truck departure" when there is no transmission ==========================================================================================================
	INSERT	@tb_gi_pending
	SELECT	appeirx.eir_send_appeir_id,
			appeirx.sub_business_unit_id,
			appeirx.eir_id,
			'' AS scope_inspection,			--after update
			NULL AS is_damaged_container,	--after update
			NULL AS estimated_estructure_id,--after update
			NULL AS estimated_machinery_id, --after update
			eirx.persona_conductor_id,
			eirx.persona_inspector_id,
			eirx.contenedor_id,
			NULL AS inspection_date,
			eirx.fecha_ingreso_camion AS truck_arrival,
			eirx.fecha_salida_camion AS truck_departure,
			eirx.fecha_ingreso_camion as activity_date,
			eirx.cat_procedencia_id,
			appeirx.is_new_insert --select *
	FROM	sde.eir_send_appeir as appeirx (nolock)
	INNER JOIN sde.eir as eirx (nolock) on appeirx.eir_id = eirx.eir_id
	WHERE	eirx.sub_unidad_negocio_id  = @sub_business_unit_id
	AND		eirx.linea_naviera_id = @shipping_line_id
	AND		appeirx.flag_send = '0'
	AND		appeirx.is_new_insert = 0 --none transmission
	AND		NOT eirx.fecha_salida_camion IS NULL --with truck departure date
	and		DATEDIFF(day,eirx.fecha_ingreso_camion,GETDATE()) <= 20 --20 days old
	AND		appeirx.active = 1

	--All pending issues with "truck departure" when there is already some transmission ==========================================================================================
	INSERT	@tb_gi_pending
	SELECT	appeirx.eir_send_appeir_id,
			appeirx.sub_business_unit_id,
			appeirx.eir_id,
			'' AS scope_inspection,			--actualiza después
			NULL AS is_damaged_container,	--actualiza después
			NULL AS estimated_estructure_id,--actualiza después
			NULL AS estimated_machinery_id, --actualiza después
			eirx.persona_conductor_id,
			eirx.persona_inspector_id,
			eirx.contenedor_id,
			eirx.fecha_revision AS inspection_date,
			eirx.fecha_ingreso_camion AS truck_arrival,
			eirx.fecha_salida_camion AS truck_departure,
			eirx.fecha_revision as activity_date,
			eirx.cat_procedencia_id,
			appeirx.is_new_insert
	FROM	sde.eir_send_appeir as appeirx (nolock)
	INNER JOIN sde.eir as eirx (nolock) on appeirx.eir_id = eirx.eir_id
	WHERE	eirx.sub_unidad_negocio_id  = @sub_business_unit_id
	AND		eirx.linea_naviera_id = @shipping_line_id
	AND		appeirx.flag_send = '0'
	AND		appeirx.is_new_insert = 1 --with transmissions
	AND		NOT eirx.fecha_salida_camion IS NULL --with truck departure date
	AND		NOT eirx.fecha_revision IS NULL  --with inspection date
	and		DATEDIFF(day,eirx.fecha_revision,GETDATE()) <= 20 --20 days old
	AND		appeirx.active = 1

	DECLARE @position int = 1, @max_reg int = (SELECT COUNT(1) FROM @tb_gi_pending)
	WHILE @position <=  @max_reg
	BEGIN
		SET @eir_id = NULL

		SELECT	@eir_id = eir_id
		FROM	@tb_gi_pending
		WHERE	_id = @position

		--update main values
		EXEC [sde].[gate_in_appeir_register] @eir_id, ''

		SET @position += 1
	END

	--update pending values
	UPDATE	@tb_gi_pending
	SET		scope_inspection = appeirx.scope_inspection,
			is_damaged_container = appeirx.is_damaged_container,
			estimated_estructure_id = appeirx.estimated_estructure_id,
			estimated_machinery_id = appeirx.estimated_machinery_id
	FROM	@tb_gi_pending AS A INNER JOIN sde.eir_send_appeir as appeirx (nolock) ON a.eir_send_appeir_id = appeirx.eir_send_appeir_id

	UPDATE	@tb_gi_pending SET scope_inspection = '' WHERE scope_inspection IS NULL

	--Structure Inspections with a result "damaged" ===============================================================================================
	INSERT	@tb_gi_damaged
	SELECT	eir_send_appeir_id,
			sub_business_unit_id,
			eir_id,
			estimated_estructure_id as estimated,
			scope_inspection,
			is_damaged_container,
			driver_id,
			---------------------------
			inspection_date ,
			truck_arrival ,
			truck_departure ,
			activity_date,
			cat_procedencia_id,
			is_new_insert
	FROM	@tb_gi_pending AS A
	WHERE	is_damaged_container = 1 AND NOT estimated_estructure_id IS NULL

	--Machinery Inspections with a result "damaged" ===============================================================================================
	INSERT	@tb_gi_damaged
	SELECT	eir_send_appeir_id,
			sub_business_unit_id,
			eir_id,
			estimated_machinery_id as estimated,
			scope_inspection,
			is_damaged_container,
			driver_id,
			---------------------------
			inspection_date ,
			truck_arrival ,
			truck_departure ,
			activity_date,
			cat_procedencia_id,
			is_new_insert
	FROM	@tb_gi_pending AS A
	WHERE	is_damaged_container = 1 AND NOT estimated_machinery_id IS NULL

	--Header damage for the transmission
	INSERT	@tb_gi_appeir_header
	SELECT	--General data
			estimatedx.sub_unidad_negocio_id as company,
			damaged_listx.eir_send_appeir_id as inspectionID,
			format(damaged_listx.activity_date,'yyyy-MM-dd HH:mm:ss'),--createdLocalDate, --Format-YYYY-MM-DD HH:MM:SS
			format(damaged_listx.activity_date,'yyyy-MM-dd HH:mm:ss'), --submittedLocalDate
			'Inspection #' + CONVERT(VARCHAR,estimatedx.estimado_emr_id) +
				ISNULL(' generated by ' + RTRIM(inspectorx.nombres) + ' ' + RTRIM(inspectorx.apellido_parterno) + ' ' + RTRIM(inspectorx.apellido_materno),'') +
				' in ' + @name_bu, --comments
			@credencial_client_id, --userid ..... h.EIR_USER_ID -> surveryorid or merc+ id
			--Container data
			1, --containerStatus ---> 1 = Empty, 2 = Full
			1,--CASE WHEN estimatex.LINEA_C_CODIGO in ('MSL','HAM') THEN 1 ELSE 2 END, --operator --> 1 = Maersk, 2 = Other
			1, --moveType --> 1 = POS, 2 = IMP, 3 = EXP
			1, --moveStatus --> 1 = IN, 2 = OUT, 3 = STUFFING, 4 = STRIPPING
			'',--bookingNo
			contx.numero_contenedor as containerNo,
			@shop_email_copy,
			'Others', --categoryEmail
			left(RTRIM(driverx.documento_identidad),20),--truckerRegno
			left(ISNULL(driverx.nombres,'') + ' ' + ISNULL(RTRIM(driverx.apellido_parterno),'') + ' ' + ISNULL(RTRIM(driverx.apellido_materno),''),300),--truckerName
			-----------------------------------------------------------------------------------------------------------------------------------
			rtrim(conttypex.descripcion),--containerType
			rtrim(contgradex.descripcion),--containerClass
			rtrim(contsizex.descripcion) as containerSize,
			rtrim(isocodex.codigo_iso) as equipmentISOCode,
			case when contfamilyx.descripcion in ('DRY CARGO','DRY')    then 'DRY'
				 when contfamilyx.descripcion in ('TANK TAINER','TANK') then 'TANK'
				 when contfamilyx.descripcion in ('FLAT RACK','FLAT')   then 'FLAT'
				 when contfamilyx.descripcion in ('REEFER','REEF')      then 'REEF' else '' end as equipmentSubType,
			-----------------------------------------------------------------------------------------------------------------------------------
			damaged_listx.estimated_id as estimate_number,
			estimatedx.eir_id,
			damaged_listx.scope_inspection,
			damaged_listx.is_damaged_container,
			---------------------------
			damaged_listx.inspection_date ,
			damaged_listx.truck_arrival ,
			damaged_listx.truck_departure ,
			damaged_listx.activity_date,
			damaged_listx.cat_procedencia_id,
			damaged_listx.is_new_insert
	FROM	@tb_gi_damaged as damaged_listx
	INNER JOIN sde.estimado_emr as estimatedx (NOLOCK) ON damaged_listx.estimated_id = estimatedx.estimado_emr_id
	INNER JOIN ges.persona AS driverx (NOLOCK) ON damaged_listx.driver_id = driverx.persona_id
	INNER JOIN sds.contenedor AS contx (NOLOCK) ON estimatedx.contenedor_id    = contx.contenedor_id
	INNER JOIN ges.catalogo as contsizex (nolock) ON contx.cat_tamano_id          = contsizex.catalogo_id
	INNER JOIN ges.catalogo as conttypex (nolock) ON contx.cat_tipo_contenedor_id = conttypex.catalogo_id
	INNER JOIN ges.catalogo as contfamilyx (nolock) ON contx.cat_familia_id       = contfamilyx.catalogo_id
	LEFT JOIN ges.catalogo as contgradex (nolock) ON contx.cat_clase_id			  = contgradex.catalogo_id
	LEFT JOIN sds.codigo_iso as isocodex (nolock) ON contx.codigo_iso_id          = isocodex.codigo_iso_id
	LEFT JOIN ges.persona AS inspectorx (NOLOCK) ON estimatedx.persona_inspector_id = inspectorx.persona_id

	--Detail damage
	INSERT	@tb_gi_appeir_detail
	SELECT	CASE WHEN responsiblex.descripcion in ('C','L') THEN IIF(damage_typex.codigo = '8','0000',damlocationx.descripcion) ELSE '0000' END as damageLocation,
  			ISNULL(damage_typex.codigo,'') as damageType,
  			rtrim(damage_typex.descripcion) as description,
  			CASE WHEN responsiblex.descripcion in ('C','L') THEN IIF(damage_typex.codigo = '8','3','2') ELSE 0 END as tpIndicator,-- Obligatorio cuando es W&T and 3P| 1=Owner,2=Third Party,3=Wear and Tear (W&T)
  			CASE WHEN responsiblex.descripcion in ('C','L') THEN LEFT(damlocationx.descripcion,1) ELSE '' END as panelIndicatorCode,
  			CONVERT(bit,1) as isDamage,
  			CASE WHEN responsiblex.descripcion in ('C','L') THEN 'D_sketch_DX24_1' ELSE '' END as panelImageFilename,
  			iif(damage_typex.codigo = '6',ISNULL(damage_typex.codigo,'0'),'0') as missingSpare, --Obligatorio solo cuando el tipo de daño es 6=Missing/Lost
  			CASE WHEN responsiblex.descripcion in ('C','L') THEN 4 ELSE 0 END as damageCarrierCode , --Obligatorio cuando es 3P| 1=Trucker,2=Customer ,3=Handling ,4=Unknown Party
  			estima_detx.estimado_emr_detalle_id as inspectionDetailID,--inspectionDetailID
  			hearder.inspectionID as inspectionID,
			0 as is_dummy
	FROM	@tb_gi_appeir_header as hearder
	INNER JOIN sde.estimado_emr_detalle as estima_detx (NOLOCK) ON hearder.estimate_number = estima_detx.estimado_emr_id
	INNER JOIN ges.catalogo AS damage_typex (NOLOCK) ON estima_detx.cat_estimado_tipo_dano_id = damage_typex.catalogo_id
	INNER JOIN ges.catalogo AS componentex (NOLOCK) ON estima_detx.cat_estimado_componente_id = componentex.catalogo_id
	INNER JOIN ges.catalogo AS responsiblex (NOLOCK) ON estima_detx.cat_estimado_asume_costo_id = responsiblex.catalogo_id
	INNER JOIN ges.catalogo AS damlocationx (NOLOCK) ON estima_detx.cat_estimado_ubicacion_dano_id = damlocationx.catalogo_id
	WHERE	responsiblex.descripcion in ('C','L') -- > customer and shipping line

	--Photos per damage
	INSERT	@tb_gi_appeir_pic
	SELECT	DETINS.inspectionDetailID,
			'imagen_'+format(DETPIC.estimado_emr_detalle_foto_id,'0'),
			ISNULL(PICATA.formato,'jpg'),
			ISNULL(PICATA.url,'*************************************************************************************?sp=r&st=2024-03-22T21:54:58Z&se=2031-03-23T05:54:58Z&spr=https&sv=2022-11-02&sr=b&sig=Jgb4AuLlkFgH8QHRoRE8b7xeackBCcDphnVq9O9Xd3A%3D'),
			ROW_NUMBER() over( PARTITION BY DETINS.inspectionDetailID ORDER BY DETPIC.estimado_emr_detalle_foto_id) as N
	FROM	@tb_gi_appeir_detail DETINS
	LEFT OUTER JOIN sde.estimado_emr_detalle_foto AS DETPIC (NOLOCK) ON DETINS.inspectionDetailID = DETPIC.estimado_emr_detalle_id
	LEFT OUTER JOIN ges.adjunto AS PICATA (NOLOCK) ON DETPIC.adjunto_id = PICATA.adjunto_id
	order by DETINS.inspectionDetailID

	DELETE @tb_gi_appeir_pic WHERE N > 3

	UPDATE	@tb_gi_appeir_pic
	SET		name = 'imagen_'+format(inspectionDetailID,'0')+'_'+format(_ID,'0')
	WHERE	name IS NULL

	--Inspecciones con resultado no dañado o los no inspeccionados =======================================================================
	INSERT	@tb_gi_appeir_header
	SELECT	--General data
			pending_listx.sub_business_unit_id as company,
			pending_listx.eir_send_appeir_id, ----inspectionID
			format(pending_listx.activity_date,'yyyy-MM-dd HH:mm:ss') as createdLocalDate, --Format-YYYY-MM-DD HH:MM:SS
			format(pending_listx.activity_date,'yyyy-MM-dd HH:mm:ss')as submittedLocalDate,
			IIF(pending_listx.inspector_id IS NULL,'','Inspection' + ISNULL(' generated by ' + RTRIM(inspectorx.nombres) + ' ' + RTRIM(inspectorx.apellido_parterno) + ' ' +
				RTRIM(inspectorx.apellido_materno),'') + ' in ') + @name_bu as comments,
			@credencial_client_id, --userid ..... h.EIR_USER_ID -> surveryorid or merc+ id
			--Container data
			1, --containerStatus ---> 1 = Empty, 2 = Full
			1,--CASE WHEN EMR.LINEA_C_CODIGO in ('MSL','HAM') THEN 1 ELSE 2 END, --operator --> 1 = Maersk, 2 = Other
			1, --moveType --> 1 = POS, 2 = IMP, 3 = EXP
			1, --moveStatus --> 1 = IN, 2 = OUT, 3 = STUFFING, 4 = STRIPPING
			'',--bookingNo
			contx.numero_contenedor as containerno,
			@shop_email_copy,
			'Others', --categoryEmail
			left(RTRIM(driverx.documento_identidad),20),--truckerRegno
			left(ISNULL(driverx.nombres,'') + ' ' + ISNULL(RTRIM(driverx.apellido_parterno),'') + ' ' + ISNULL(RTRIM(driverx.apellido_materno),''),300),--truckerName
			-----------------------------------------------------------------------------------------------------------------------------------
			rtrim(conttypex.descripcion),--containerType
			rtrim(contgradex.descripcion),--containerClass
			rtrim(contsizex.descripcion) as containerSize,
			rtrim(isocodex.codigo_iso) as equipmentISOCode,
			case when contfamilyx.descripcion in ('DRY CARGO','DRY')    then 'DRY'
				 when contfamilyx.descripcion in ('TANK TAINER','TANK') then 'TANK'
				 when contfamilyx.descripcion in ('FLAT RACK','FLAT')   then 'FLAT'
				 when contfamilyx.descripcion in ('REEFER','REEF')      then 'REEF' else '' end as equipmentSubType,
			-----------------------------------------------------------------------------------------------------------------------------------
			0 <USER> <GROUP>,
			pending_listx.eir_id,
			pending_listx.scope_inspection,
			pending_listx.is_damaged_container,
			---------------------------
			pending_listx.inspection_date ,
			pending_listx.truck_arrival ,
			pending_listx.truck_departure ,
			pending_listx.activity_date,
			pending_listx.cat_procedencia_id,
			pending_listx.is_new_insert
	FROM	@tb_gi_pending as pending_listx
	INNER JOIN ges.persona AS driverx (NOLOCK) ON pending_listx.driver_id = driverx.persona_id
	INNER JOIN sds.contenedor AS contx (NOLOCK) ON pending_listx.container_id = contx.contenedor_id --sp_help VACTM_CONTENEDOR
	INNER JOIN ges.catalogo as contsizex (nolock) ON contx.cat_tamano_id          = contsizex.catalogo_id
	INNER JOIN ges.catalogo as conttypex (nolock) ON contx.cat_tipo_contenedor_id = conttypex.catalogo_id
	INNER JOIN ges.catalogo as contfamilyx (nolock) ON contx.cat_familia_id       = contfamilyx.catalogo_id
	LEFT JOIN ges.catalogo as contgradex (nolock) ON contx.cat_clase_id			  = contgradex.catalogo_id
	LEFT JOIN sds.codigo_iso as isocodex (nolock) ON contx.codigo_iso_id          = isocodex.codigo_iso_id
	LEFT JOIN ges.persona AS inspectorx (NOLOCK) ON pending_listx.inspector_id = inspectorx.persona_id
	WHERE	pending_listx.is_damaged_container = 0 --containers no damaged

	INSERT	@tb_gi_appeir_detail
	SELECT	'0000' as damageLocation,
  			16 as damageType,
  			'' as description,
  			'0'as tpIndicator, --Obligatorio cuando es W&T and 3P| 1=Owner,2=Third Party,3=Wear and Tear (W&T)
  			'D'as panelIndicatorCode,
  			CONVERT(bit,0) as isDamage,
  			'D_sketch_DX24_1' as panelImageFilename,
  			'' as missingSpare, --Obligatorio solo cuando DamageType es 6=Missing/Lost
  			4 as damageCarrierCode, --Obligatorio cuando es 3P| 1=Trucker,2=Customer ,3=Handling ,4=Unknown Party
  			header.eir_id as inspectionDetailID,
  			header.inspectionID,
			1 as is_dummy
	FROM	@tb_gi_appeir_header as header
	WHERE	header.estimate_number = 0

	INSERT	@tb_gi_appeir_pic
	SELECT	detailx.inspectionDetailID,
			'imagen_'+FORMAT(detailx.inspectionDetailID,'0')+'_'+FORMAT(detailx._id,'0'),
			'jpg',
			'*************************************************************************************?sp=r&st=2024-03-22T21:54:58Z&se=2031-03-23T05:54:58Z&spr=https&sv=2022-11-02&sr=b&sig=Jgb4AuLlkFgH8QHRoRE8b7xeackBCcDphnVq9O9Xd3A%3D',
			1 as N
	FROM	@tb_gi_appeir_detail as detailx
	WHERE	is_dummy = 1

	--=============================================== update / trace
	UPDATE	sde.eir_send_appeir
	SET		activity_date = AX.activity_date,
			-----------------------------------------
			modification_date = getdate()
			--appeir_trace = left(isnull(appeir_trace,'')+'->udate',50)
	FROM	@tb_gi_appeir_header AS AX INNER JOIN sde.eir_send_appeir AS appeirx ON AX.inspectionID = appeirx.eir_send_appeir_id
	--===============================================
END

SELECT * FROM @tb_gi_appeir_header as T_inspection_cab order by createdLocalDate
SELECT * FROM @tb_gi_appeir_detail as T_inspection_det
SELECT * FROM @tb_gi_appeir_pic    as T_inspection_pic

END



7.- Store sde.update_send_appeir_status

ALTER PROCEDURE sde.update_send_appeir_status
@eir_send_appeir_id int,
@flag_send BIT,
@status_code INT,
@result_message VARCHAR(100),
@inspection_id int ---eir reference number del appeir
AS

/*-----------------------------------------------------------------------------------------
 Ticket     		Author 	Date	   	Description of change
 ---------------	------	----------	-------------------------------------------
 000000000000000	<USER>  <GROUP>/06/2025	Implementation of appEIR transmissions
------------------------------------------------------------------------------------------

--Possible answers

flag_send	status_code	result_message
--------	-----------	--------------------------------------
0			<USER>			<GROUP> Request
1			200			Successfully saved inspection details

*/

BEGIN
	SET NOCOUNT ON

      UPDATE sde.eir_send_appeir
      SET	flag_send = @flag_send, --> no se evalua si es de exito o fracaso, este valor proviene de la rpta de envío del appEIR: 0=fracaso, 1=éxtito
			status_code = @status_code,
			result_message = @result_message,
			eir_reference_appeir = @inspection_id,
			-----------------------------------------
			modification_date = getdate(),
			appeir_trace = left(isnull(appeir_trace,'')+'->sent',50)
      WHERE	eir_send_appeir_id = @eir_send_appeir_id
END



8.- Store [sde].[gate_in_appeir_register]

ALTER PROCEDURE [sde].[gate_in_appeir_register]
@eir_id int,
@comment varchar(10)
AS

/*-----------------------------------------------------------------------------------------
 Ticket     		Author 	Date	   	Description of change
 ---------------	------	----------	-------------------------------------------
 000000000000000	<USER>  <GROUP>/06/2025	Implementation of appEIR transmissions
------------------------------------------------------------------------------------------*/

DECLARE @sub_business_unit_id numeric(18),@cat_move_type_id numeric(18), @shipping_line_id int,
		@eir_send_appeir_id INT,@eir_active bit,@truck_departure_date DATETIME,
		@container_id int, @depot_credential_appeir_id int,@truck_arrival datetime

DECLARE @es_reefer bit, @estructure_insp_completed bit, @machinery_insp_completed bit,@estructure_damaged bit,@machinery_damaged bit,
		@estimated_estructure_id int=NULL, @estimated_machinery_id int=NULL
DECLARE @scope_inspection_old VARCHAR(1),@is_damaged_container_old bit
DECLARE @is_damaged_container bit		-->0=no damage | 1=damaged
DECLARE @scope_inspection varchar(1)	-->N=ninguna insp finalizada | E=solo insp estructura finalizada| M=solo insp maquinaria finalizada | A=ambas insp finalizadas

DECLARE @is_gate_out NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43081'),
		@is_gate_in NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43080'),
		------------------------------------------------------------------------------------------------
		@is_estimate_type_structure NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '47490'),
		@is_estimate_type_machinery NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '47491'),
		------------------------------------------------------------------------------------------------
		@is_activity_insp_estructure NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = 'cat_43161_box_inspection'),
		@is_activity_insp_machinery NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = 'cat_43161_pti')

SELECT 	@sub_business_unit_id = eirx.sub_unidad_negocio_id,
		@eir_id = eirx.eir_id,
		@cat_move_type_id = eirx.cat_movimiento_id,
		@shipping_line_id = eirx.linea_naviera_id,
		@eir_active = eirx.activo,
		@truck_departure_date = eirx.fecha_salida_camion,
		@container_id = eirx.contenedor_id,
		@truck_arrival = eirx.fecha_ingreso_camion
FROM	sde.eir AS eirx
WHERE	eir_id = @eir_id

IF @eir_active = 0 AND EXISTS(SELECT 1 FROM sde.eir_send_appeir (nolock) WHERE eir_id = @eir_id)
	UPDATE	sde.eir_send_appeir
	SET		active = 0,
			comment=left(iif(isnull(comment,'')='','',comment+' | ')+' eir deleted',200),
			--------------------------------------------------------------------------------
			modification_date = getdate(),
			appeir_trace = left(isnull(appeir_trace,'')+'->desactive1',50)
	WHERE	eir_id = @eir_id
	AND		active = 1
	AND		flag_send <> 1 --not sent

IF @eir_active = 1 AND @cat_move_type_id = @is_gate_in
BEGIN
	SELECT	@depot_credential_appeir_id = appEIR_settingx.depot_credential_appeir_id
	FROM	sde.depot_credential_appeir as appEIR_settingx (nolock)
	WHERE	appEIR_settingx.sub_business_unit_id = @sub_business_unit_id
	AND		appEIR_settingx.shipping_line_id = @shipping_line_id
	AND		appEIR_settingx.send_gate_in = 1
	AND		appEIR_settingx.active = 1

	IF NOT @depot_credential_appeir_id IS NULL
	BEGIN
		SET @scope_inspection = 'N'
		SET @is_damaged_container = 0 --x defaukt
		SET @estructure_insp_completed = 0
		SET @machinery_insp_completed = 0

		SELECT	@es_reefer = iif(isnull(conttypex.codigo,'0')='0',0,1)
		FROM	sds.contenedor as contx (NOLOCK)
		INNER JOIN [ges].[catalogo] AS conttypex ON contx.cat_tipo_contenedor_id = conttypex.catalogo_id and unidad_negocio_id=1
		WHERE	contx.contenedor_id = @container_id

		SET @es_reefer = ISNULL(@es_reefer,0) --x default is dry

		SELECT	@estructure_insp_completed = activityx.concluido,
				@estructure_damaged = activityx.resultado_estructura_danada
		FROM	sde.eir_actividad_zona AS activityx
		WHERE	activityx.eir_id = @eir_id
		AND		activityx.cat_actividad_zona_id = @is_activity_insp_estructure
		AND		activityx.activo = 1

		IF @estructure_insp_completed = 1
		BEGIN
			SET @scope_inspection = 'E'
			IF @estructure_damaged = 1 SET @is_damaged_container = 1

			IF @estructure_damaged = 1
				SELECT	TOP 1 @estimated_estructure_id = ex.estimado_emr_id
				FROM	sde.estimado_emr AS ex (NOLOCK)
				WHERE	ex.eir_id = @eir_id
				AND		ex.cat_tipo_estimado_id = @is_estimate_type_structure
				AND		ex.activo = 1
				ORDER BY ex.estimado_fecha_inspeccion DESC
		END

		IF @es_reefer = 1
		BEGIN
			SELECT	@machinery_insp_completed = activityx.concluido,
					@machinery_damaged = activityx.resultado_maquinaria_danada
			FROM	sde.eir_actividad_zona AS activityx
			WHERE	activityx.eir_id = @eir_id
			AND		activityx.cat_actividad_zona_id = @is_activity_insp_machinery
			AND		activityx.activo = 1

			IF @machinery_insp_completed = 1
			BEGIN
				IF NOT EXISTS(SELECT 1 FROM sde.eir_send_appeir WHERE eir_id = @eir_id AND flag_send = 1 AND scope_inspection = 'M' AND active = 1)
				BEGIN
					DECLARE @control bit = 0
					IF EXISTS(SELECT 1 FROM sde.eir_send_appeir WHERE eir_id = @eir_id AND flag_send = 1 AND scope_inspection = 'E' AND active = 1)
					BEGIN
						SET @control = 1
						SET @estimated_estructure_id = NULL
						SET @is_damaged_container = 0
					END

					SET @scope_inspection = IIF(@scope_inspection='E' and @control = 0,'A','M')
					IF @machinery_damaged = '1' SET @is_damaged_container = 1

					IF @machinery_damaged = '1'
						SELECT	TOP 1 @estimated_machinery_id = ex.estimado_emr_id
						FROM	sde.estimado_emr AS ex (NOLOCK)
						WHERE	ex.eir_id = @eir_id
						AND		ex.cat_tipo_estimado_id = @is_estimate_type_machinery
						AND		ex.activo = 1
						ORDER BY ex.estimado_fecha_inspeccion DESC
				END
			END
		END

		IF NOT EXISTS(SELECT 1 FROM [sde].[eir_send_appeir] WHERE eir_id = @eir_id AND active = 1) AND DATEDIFF(day,@truck_arrival,GETDATE()) <= 20 --20 days old
			INSERT [sde].[eir_send_appeir](depot_credential_appeir_id,sub_business_unit_id,eir_id,flag_send,registration_date,active,comment,is_new_insert,appeir_trace)
			VALUES(@depot_credential_appeir_id,@sub_business_unit_id,@eir_id,0,GETDATE(),1,'[adaptation]',0,'new2')

		--NEM OR NME OR NA OR E OR EM OR ME
		SELECT	@eir_send_appeir_id = eir_send_appeir_id
		FROM	sde.eir_send_appeir
		WHERE	eir_id = @eir_id
		AND		flag_send = 0
		AND		active = 1

		IF NOT @eir_send_appeir_id IS NULL
		BEGIN
			UPDATE	[sde].[eir_send_appeir]
			SET		scope_inspection = @scope_inspection,
					is_damaged_container = @is_damaged_container,
					estimated_estructure_id = @estimated_estructure_id,
					estimated_machinery_id = @estimated_machinery_id,
					----------------------------------------------
					modification_date = GETDATE()
					--appeir_trace = left(isnull(appeir_trace,'')+'->uflag',50)
			WHERE	eir_send_appeir_id = @eir_send_appeir_id
		END
		ELSE
		BEGIN
			IF DATEDIFF(day,@truck_arrival,GETDATE()) <= 20 --20 days old
			BEGIN
				IF	NOT @truck_departure_date IS NULL AND
					EXISTS(SELECT 1 FROM [sde].[eir_send_appeir] WHERE eir_id = @eir_id AND flag_send = 1 AND active = 1) AND
					@is_damaged_container = 1 AND --algo q enviar
					NOT EXISTS(SELECT 1 FROM [sde].[eir_send_appeir] WHERE eir_id = @eir_id AND ISNULL(scope_inspection,'') = ISNULL(@scope_inspection,'')
																								AND ISNULL(@estimated_estructure_id,0) = ISNULL(@estimated_estructure_id,0)
																								AND ISNULL(estimated_machinery_id,0) = ISNULL(@estimated_machinery_id,0) AND active = 1)
				BEGIN
					INSERT [sde].[eir_send_appeir](depot_credential_appeir_id,sub_business_unit_id,eir_id,flag_send,registration_date,active,comment,scope_inspection,
							is_damaged_container,estimated_estructure_id,estimated_machinery_id,is_new_insert,appeir_trace)
					VALUES(@depot_credential_appeir_id,@sub_business_unit_id,@eir_id,0,GETDATE(),1,iif(ISNULL(@comment,'')='trigger','[add1]','[add2]'),@scope_inspection,
							@is_damaged_container,@estimated_estructure_id,@estimated_machinery_id,1,'new3')
				END
			END
		END
	END
	ELSE
	BEGIN
		IF EXISTS(SELECT 1 FROM sde.eir_send_appeir (nolock) WHERE eir_id = @eir_id)
			UPDATE	sde.eir_send_appeir
			SET		active = 0,
					comment=left(iif(isnull(comment,'')='','',comment+' | ')+' shipping line not configured for transmissions, says '+
									(select shippx.nombre FROM sds.linea_naviera AS shippx (nolock) WHERE shippx.linea_naviera_id = @shipping_line_id),200),
					------------------------
					modification_date = getdate(),
					appeir_trace = left(isnull(appeir_trace,'')+'->desactive2',50)
			WHERE	eir_id = @eir_id
			AND		active = 1
			AND		flag_send <> 1
	END
END



9.- Trigger [sde].[TI_appEIR] ON [sde].[eir]

ALTER TRIGGER [sde].[TI_appEIR] ON [sde].[eir]
FOR INSERT
AS

/*-----------------------------------------------------------------------------------------
 Ticket     		Author 	Date	   	Description of change
 ---------------	------	----------	-------------------------------------------
 000000000000000	<USER>  <GROUP>/06/2025	Implementation of appEIR transmissions
------------------------------------------------------------------------------------------*/

BEGIN

	DECLARE @sub_business_unit_id numeric(18)
	DECLARE @eir_id int, @cat_move_type_id numeric(18), @shipping_line_id int,
			@depot_credential_appeir_id INT,@truck_arrival datetime

	DECLARE @is_gate_out NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43081'),
			@is_gate_in NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43080')

	SELECT 	@sub_business_unit_id = INS.sub_unidad_negocio_id,
			@eir_id				  = INS.eir_id,
			@cat_move_type_id     = INS.cat_movimiento_id,
			@shipping_line_id     = INS.linea_naviera_id,
			@truck_arrival		  = INS.fecha_ingreso_camion
	FROM	INSERTED INS

	IF @cat_move_type_id = @is_gate_in AND DATEDIFF(day,@truck_arrival,GETDATE()) <= 20 --20 days old
	BEGIN
		SELECT	@depot_credential_appeir_id = appEIR_settingx.depot_credential_appeir_id
		FROM	sde.depot_credential_appeir as appEIR_settingx (nolock)
		WHERE	appEIR_settingx.sub_business_unit_id = @sub_business_unit_id
		AND		appEIR_settingx.shipping_line_id = @shipping_line_id
		AND		appEIR_settingx.send_gate_in = 1
		AND		appEIR_settingx.active = 1

		IF NOT @depot_credential_appeir_id IS NULL
			INSERT sde.eir_send_appeir (depot_credential_appeir_id,sub_business_unit_id,eir_id,flag_send,registration_date,active,is_new_insert,appeir_trace)
			VALUES (@depot_credential_appeir_id,@sub_business_unit_id,@eir_id,0,GETDATE(),1,0,'new0')
	END
END



10.- Trigger [sde].[TU_appEIR]

ALTER TRIGGER [sde].[TU_appEIR] ON [sde].[eir]
FOR UPDATE
AS

/*-----------------------------------------------------------------------------------------
 Ticket     		Author 	Date	   	Description of change
 ---------------	------	----------	-------------------------------------------
 000000000000000	<USER>  <GROUP>/06/2025	Implementation of appEIR transmissions
------------------------------------------------------------------------------------------*/

BEGIN
	DECLARE @sub_business_unit_id numeric(18),@eir_id int,@cat_move_type_id numeric(18), @shipping_line_id int,
			@eir_active bit,@truck_departure_date DATETIME, @depot_credential_appeir_id int,@truck_arrival datetime

	DECLARE @is_gate_out NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43081'),
			@is_gate_in NUMERIC(18) = (SELECT catalogo_id FROM ges.catalogo (NOLOCK) WHERE alias = '43080')

	SELECT 	@sub_business_unit_id = INS.sub_unidad_negocio_id,
			@eir_id = INS.eir_id,
			@cat_move_type_id = INS.cat_movimiento_id,
			@shipping_line_id = INS.linea_naviera_id,
			@eir_active = INS.activo,
			@truck_departure_date = INS.fecha_salida_camion,
			@truck_arrival = INS.fecha_ingreso_camion
	FROM	INSERTED AS INS

	IF @eir_active = 0 AND EXISTS(SELECT 1 FROM sde.eir_send_appeir (nolock) WHERE eir_id = @eir_id)
		UPDATE	sde.eir_send_appeir
		SET		active = 0,
				comment=left(iif(isnull(comment,'')='','',comment+' | ')+' eir deleted',200),
				----------------------------------------
				modification_date = getdate(),
				appeir_trace = left(isnull(appeir_trace,'')+'->desactive3',50)
		WHERE	eir_id = @eir_id
		AND		active = 1
		AND		flag_send <> 1 --not sent

	IF @eir_active = 1 AND @cat_move_type_id = @is_gate_in
	BEGIN
		SELECT	@depot_credential_appeir_id = appEIR_settingx.depot_credential_appeir_id
		FROM	sde.depot_credential_appeir as appEIR_settingx (nolock)
		WHERE	appEIR_settingx.sub_business_unit_id = @sub_business_unit_id
		AND		appEIR_settingx.shipping_line_id = @shipping_line_id
		AND		appEIR_settingx.send_gate_in = 1
		AND		appEIR_settingx.active = 1

		IF NOT @depot_credential_appeir_id IS NULL
		BEGIN
			IF NOT EXISTS(SELECT 1 FROM sde.eir_send_appeir (nolock) WHERE eir_id = @eir_id AND active = 1)
			BEGIN
				IF DATEDIFF(day,@truck_arrival,GETDATE()) <= 20 --20 days old
					INSERT sde.eir_send_appeir(depot_credential_appeir_id,sub_business_unit_id,eir_id,flag_send,registration_date,active,comment,is_new_insert,appeir_trace)
					VALUES(@depot_credential_appeir_id,@sub_business_unit_id,@eir_id,0,GETDATE(),1,'[upd]',0,'new1')
			END

			IF	NOT @truck_departure_date IS NULL AND
				EXISTS(SELECT 1 FROM sde.eir_send_appeir (nolock) WHERE eir_id = @eir_id AND flag_send = 1 AND active = 1)
					EXEC sde.gate_in_appeir_register @eir_id,'trigger'
		END
		ELSE
		BEGIN
			IF EXISTS(SELECT 1 FROM sde.eir_send_appeir (nolock) WHERE eir_id = @eir_id)
				UPDATE	sde.eir_send_appeir
				SET		active = 0,
						comment=left(iif(isnull(comment,'')='','',comment+' | ')+' shipping line not configured for transmissions, says '+
										(select shippx.nombre FROM sds.linea_naviera AS shippx (nolock) WHERE shippx.linea_naviera_id = @shipping_line_id),200),
						------------------------------------------------------
						modification_date = getdate(),
						appeir_trace = left(isnull(appeir_trace,'')+'->desactive4',50)
				WHERE	eir_id = @eir_id
				AND		active = 1
				AND		flag_send <> 1
		END
	END
END
